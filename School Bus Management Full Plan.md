# نظام إدارة الحافلات المدرسية - خطة التنفيذ الشاملة
**School Bus Management System - Complete Implementation Plan**

---

# المقدمة

يتضمن هذا الملف تفصيلاً كاملاً وشاملاً لجميع مراحل تنفيذ مشروع **نظام إدارة الحافلات المدرسية كنظام SaaS متعدد المستأجرين**. تم تقسيم المشروع إلى 28 مرحلة منظمة، بحيث يركز كل منها على مجموعة محددة من الوظائف والمهام. يراعى في كل مرحلة:

* **التوثيق**: يتم إنشاء ملفين لكل مرحلة:

  1. **changelog** يسجل التغييرات المنفذة بشكل مختصر.
  2. **details** لشرح مفصل للمهام والأهداف التقنية.
* **نظام الألوان**:

  * Primary (أزرق): `#3b82f6`
  * Secondary (فيروزي): `#14b8a6`
  * Accent (أخضر): `#10b981`
  * Warning (برتقالي): `#f59e0b`
  * Error (أحمر): `#ef4444`
* **الخطوط**: خط Cairo للغة العربية والإنجليزية.
* **التصميم**: Responsive Design، دعم RTL/LTR، Dark Mode، Transitions وAnimations سلسة.
* **البنية التقنية العامة**:

  * Frontend: React + TypeScript، Vite، TailwindCSS، Radix UI، Lucide React.
  * Backend & Database: Supabase (PostgreSQL + PostGIS)، RLS للـ Multi-Tenant.
  * طبقة الخدمات: Supabase Functions (Deno) للـ serverless logic.
  * التخزين: Supabase Storage أو S3.
  * Realtime: Supabase Realtime، Redis (عند الحاجة).

---

## المرحلة 1: إعداد البنية الأساسية للمشروع (Initial Setup & Architecture)

### الهدف الوظيفي

* تهيئة بنية المجلدات والملفات الرئيسية للمشروع وفق معمارية نظيفة (Clean Architecture + Modularization).
* ضبط إعدادات الـ Multi-Tenant العاملة على Supabase (PostgreSQL + PostGIS + RLS).

### المهام التفصيلية

1. إنشاء المستودع الرئيسي (`school-system/`) وضبط ملفات الضبط:

   * `README.md`: ملخص المشروع.
   * `.env` و`.env.example`: تعريف المتغيرات الأساسية (SUPABASE\_URL, SUPABASE\_KEY, MAPBOX\_TOKEN, PAYMOB\_CREDENTIALS).
   * `package.json` أو `pubspec.yaml`: تحديد تبعيات React + TypeScript (أو Flutter إذا اختُير).
   * ملفات التهيئة: `tsconfig.json`، `vite.config.js` (لـ React).
2. إنشاء الهيكل الأساسي للمجلدات:

   ```
   school-system/
   │
   ├── apps/                  # الوحدات (Plug-ins)
   │   └── README.md
   ├── dashboards/            # لوحات التحكم حسب الدور
   │   └── README.md
   ├── ui/                    # مكتبة الواجهات العامة
   │   ├── components/        # Buttons, Inputs, Modals...
   │   ├── layout/            # Page wrappers, sidebars, headers
   │   ├── themes/            # دعم الوضع الداكن والخطوط
   │   ├── utils/             # أدوات مساعدة للتصميم
   │   └── index.ts
   ├── scripts/               # سكربتات التشغيل والتحضير والنشر
   │   └── README.md
   ├── test/                  # هيكلة الاختبارات
   │   └── README.md
   ├── tmp/                   # ملفات تجريبية مؤقتة
   │   └── README.md
   ├── docs/                  # التوثيق الرسمي
   │   ├── changelog/         # سجلات التغيير لكل وحدة
   │   │   └── README.md
   │   ├── details/           # تفاصيل كل مرحلة ووحدة
   │   │   └── README.md
   │   ├── architecture.md    # وصف المعمارية العامة
   │   ├── setup-guide.md     # دليل الإعداد
   │   ├── modules-list.md    # قائمة الوحدات والمكونات
   │   └── database-schema.md # وصف مخطط قاعدة البيانات
   ├── .env                   # إعدادات البيئة
   ├── README.md              # وصف المشروع الأساسي
   └── tsconfig.json / vite.config.js
   ```
3. إعداد قاعدة البيانات على Supabase:

   * إنشاء مشروع جديد في Supabase.
   * تفعيل امتداد PostGIS.
   * إنشاء جداول أساسية: `tenants`، `users`، `roles`، `permissions`، `role_permission_map`.
   * تفعيل سياسة RLS (Row Level Security) على جميع الجداول باستخدام `tenant_id` لفلترة البيانات.
4. ضبط إعدادات PostGIS:

   * تعريف جدول `buses` مع حقل `current_location` من نوع `GEOGRAPHY(Point, 4326)`.
   * إنشاء جداول `routes` و`stops` مع حقول `GEOMETRY` لتخزين خطوط السير والنقاط.
5. إعداد ملف **docs/architecture.md**:

   * شرح معمارية Clean Architecture، وانسياب البيانات بين الـ Frontend والـ Backend وقاعدة البيانات.
   * توثيق طريقة عزل البيانات لكل مستأجر (Tenant) عبر RLS.
6. حفظ التغييرات:

   * **docs/changelog/initial-setup.md**: سجل مختصر للإنشاءات (الجداول، الهيكل).
   * **docs/details/initial-setup.md**: شرح مفصل لكل خطوة (أسباب الاختيار وأمثلة SQL).
   * تحديث **docs/changelog.md** العام بملخص المرحلة.

### الوثائق المطلوبة

* docs/changelog/initial-setup.md
* docs/details/initial-setup.md

---

## المرحلة 2: نظام المصادقة وتأمين الدخول (Authentication & Security)

### الهدف الوظيفي

* تنفيذ نظام تسجيل الدخول والتسجيل الآمن مع الضوابط الأمنية (CSRF, Rate Limiting, 2FA).
* إعداد JWT عبر Supabase Auth أو استخدام Laravel Sanctum إذا كان الباك مبنيًا على Laravel.

### المهام التفصيلية

1. **قاعدة البيانات**:

   * إنشاء جدول `auth_sessions` لحفظ توكنات الوصول (Access & Refresh Tokens) وربطها بـ `user_id`, `tenant_id`.
   * إنشاء جدول `login_logs` لتسجيل محاولات تسجيل الدخول (IP, جهاز, timestamp).
2. **الواجهة الخلفية**:

   * إعداد Supabase Auth للبريد الإلكتروني وكلمة المرور:

     * التحقق من قوة كلمة المرور (≥ 8 أحرف + حرف كبير + حرف صغير + رقم + رمز).
     * إعداد Rate Limiting: مثلاً: 5 محاولات في 15 دقيقة مع تأخير تصاعدي.
     * تفعيل CSRF Tokens على مسارات الـ REST أو GraphQL.
   * تفعيل 2FA:

     * توليد TOTP (دعم Google Authenticator).
     * خيار إرسال رمز عبر البريد الإلكتروني أو SMS (Firebase Functions أو Twilio).
     * إلزام 2FA للـ System Admin والمدير (School Manager).
   * إعداد Middleware `CheckPermissions` للتحقق من صلاحيات المستخدم قبل الوصول لأي مسار.
   * إعداد إدارة الجلسات:

     * توكنات قصيرة العمر (Access Tokens) مع Refresh Tokens.
     * ميزة Logout from all devices.
     * منع الجلسات المتزامنة (اختياري).
   * تسجيل تدقيق الدخول (Audit Logging) لكل محاولة.
   * دعم SSO عبر Google (اختياري).
3. **الواجهة الأمامية (React + TypeScript)**:

   * تركيب `react-hook-form` مع `zod` لفورم التسجيل وتسجيل الدخول.
   * إنشاء صفحة **Login** و**Register**:

     * حقول البريد الإلكتروني وكلمة المرور مع مؤشر قوة كلمة المرور.
     * حالة انتظار (Loading) عند الإرسال.
   * صفحة التحقق من 2FA:

     * حقل لإدخال رمز التحقق المرسل.
   * Hook `useAuth()` لإدارة حالة المصادقة (login, logout, refreshToken).
   * صفحة **Forgot Password** لإرسال رابط إعادة تعيين كلمة المرور.
   * إضافة حماية للروابط (إخفاء عند عدم تسجيل الدخول).
4. **توثيق**:

   * **docs/details/authentication.md**:

     * شرح بنية الجداول (`auth_sessions`, `login_logs`).
     * تفصيل Rate Limiting وCSRF و2FA.
     * مثال JSON لعملية تسجيل الدخول.
   * **docs/changelog/authentication.md**: سجل إنشاء الجداول، إعداد Supabase Auth، الواجهات.
5. **اختبارات**:

   * Unit Tests للباك: تسجيل دخول بصلاحيات صحيحة/خطأ، تفعيل 2FA.
   * Integration Tests للـ Frontend: محاكاة تسجيل الدخول وإعادة التوجيه.

### الوثائق المطلوبة

* docs/changelog/authentication.md
* docs/details/authentication.md

---

## المرحلة 3: تصميم وإعداد نظام الأدوار والصلاحيات (RBAC & Roles Management)

### الهدف الوظيفي

* بناء نموذج بيانات مرن لإدارة الصلاحيات (RBAC) مع نطاقات بيانات (Global, Tenant, Assigned, Children, Personal).
* إعداد واجهات لإدارة الأدوار وتوزيع الصلاحيات داخل كل Tenant.

### المهام التفصيلية

1. **قاعدة البيانات**:

   * توسيع جدول `roles` ليشمل عمود `role_scope` (قيم: Global, Tenant, Assigned, Children, Personal).
   * إنشاء جدول `permissions` يحتوي على قائمة الصلاحيات الدقيقة (BUS\_TRACK, STUDENTS\_MANAGE\_ATTENDANCE, BUSES\_MANAGE, إلخ).
   * إنشاء جدول `role_permission_map` لربط الأدوار بالـ permissions.
   * إنشاء جدول `user_roles` لربط المستخدمين بالأدوار (fields: `user_id`, `role_id`, `tenant_id`).
2. **الواجهة الخلفية**:

   * Endpoints لإدارة الأدوار:

     * `GET /api/roles` لإحضار جميع الأدوار.
     * `POST /api/roles` لإنشاء دور جديد.
     * `PATCH /api/roles/:id` لتعديل الدور (مثل: تغيير النطاق أو الاسم).
     * `DELETE /api/roles/:id` لحذف دور (مع التأكد من عدم وجود مستخدمين مرتبطين).
   * إدارة الصلاحيات:

     * `GET /api/permissions` لاسترجاع قائمة الصلاحيات المتاحة.
     * `POST /api/role-permissions` لإضافة صلاحيات لدور.
     * `DELETE /api/role-permissions` لإزالة صلاحية من دور.
   * إدارة ربط المستخدمين بالأدوار:

     * `GET /api/user-roles?tenant_id=...` لاسترجاع أدوار المستخدمين في Tenant.
     * `POST /api/user-roles` لربط مستخدم بدور.
     * `DELETE /api/user-roles` لإلغاء ربط.
   * إعداد Middleware `CheckPermissions` (أو Hook في Supabase) للتحقق من صلاحيات المستخدم قبل السماح بالوصول.
   * ضبط سياسات RLS:

     * جدول `users`: System Admin يرى الكل، School Admin يرى فقط البيانات ضمن Tenant.
     * الجداول الأخرى: يعتمد الوصول على صلاحيات المستخدم مقابل `tenant_id`.
3. **الواجهة الأمامية**:

   * **صفحة Roles Management** (لوحة System Admin):

     * عرض قائمة الأدوار، زر “إضافة دور جديد”، فورم لتحديد اسم الدور، نطاقه (`role_scope`).
     * الجدول يضم عمود “تعديل” و“حذف” لكل دور.
   * **صفحة Assign Roles** (لوحة School Admin):

     * جدول مستخدمي المدرسة، عمود “الأدوار الحالية”، زر “تعديل الأدوار” يفتح Modal يتيح اختيار الأدوار المتاحة.
   * Hook `usePermissions()`:

     * يُعيد قائمة الصلاحيات للمستخدم؛ تُستخدم لحماية وصول الصفحات أو المكونات.
   * مكوّن `PermissionGuard`:

     * يقبل قائمة صفة الصلاحية المطلوبة ويمنع العرض إذا كانت صلاحيات المستخدم لا تتضمنها.
   * منع ظهور أي بيانات خارج نطاق Tenant تلقائيًا بفضل RLS و`tenant_id` في الـ JWT.
4. **توثيق**:

   * **docs/details/rbac.md**:

     * شرح بنية الجداول (`roles`, `permissions`, `role_permission_map`, `user_roles`).
     * أمثلة استعلام وسياسات RLS.
   * **docs/changelog/rbac.md**: سجل الجداول الجديدة والواجهات والخدمات المتعلقة بالصلاحيات.
5. **اختبارات**:

   * Unit Tests للـ Backend: التحقق من رفض الوصول لمسارات بدون صلاحية، اختبار RLS.
   * E2E Tests للـ Frontend: محاولة دخول مستخدم غير مخوّل لصفحة معينة والتأكد من إعادة التوجيه.

### الوثائق المطلوبة

* docs/changelog/rbac.md
* docs/details/rbac.md

---

## المرحلة 4: إعداد بنية قاعدة البيانات التفصيلية (Database Schema & Multi-Tenant)

### الهدف الوظيفي

* تصميم الجداول التفصيلية للموارد الرئيسية (schools, students, parents, buses, drivers, routes, stops, notifications, attendance, maintenance, payments) مع تضمين `tenant_id` لكل جدول.
* تفعيل سياسات RLS لكل جدول لضمان عزل بيانات كل مدرسة بالكامل.

### المهام التفصيلية

1. **تصميم مخطط الجداول (Schema)**:

   * **tenants (schools)**:

     * id (UUID)
     * name (اسم المدرسة)
     * domain/subdomain
     * logo\_url
     * primary\_color, secondary\_color, accent\_color, warning\_color, error\_color
     * created\_at, updated\_at
   * **users**:

     * id (UUID)
     * email, password\_hash
     * name\_ar, name\_en
     * role\_id (FK إلى `roles.id`)
     * tenant\_id (FK إلى `tenants.id`)
     * phone\_number, profile\_photo\_url
     * created\_at, updated\_at
   * **students**:

     * id (UUID)
     * name\_ar, name\_en
     * parent\_id (FK إلى `parents.id`)
     * photo\_url, files (JSON Array)
     * tenant\_id (FK إلى `tenants.id`)
     * created\_at, updated\_at
   * **parents**:

     * id (UUID)
     * name\_ar, name\_en, phone, email
     * tenant\_id
     * created\_at, updated\_at
   * **drivers**:

     * id (UUID)
     * name\_ar, name\_en, phone, email, license\_number
     * tenant\_id
     * created\_at, updated\_at
   * **buses**:

     * id (UUID)
     * bus\_number, capacity
     * current\_location GEOGRAPHY(Point, 4326)
     * driver\_id (FK إلى `drivers.id`)
     * tenant\_id
     * created\_at, updated\_at
   * **routes**:

     * id (UUID)
     * name
     * bus\_id (FK إلى `buses.id`)
     * driver\_id (FK إلى `drivers.id`)
     * start\_time, end\_time (Time)
     * tenant\_id
     * created\_at, updated\_at
   * **stops**:

     * id (UUID)
     * route\_id (FK إلى `routes.id`)
     * stop\_order (Integer)
     * location GEOGRAPHY(Point, 4326)
     * name\_ar, name\_en
     * arrival\_time\_estimate (Time)
     * tenant\_id
     * created\_at, updated\_at
   * **attendance**:

     * id (UUID)
     * student\_id (FK إلى `students.id`)
     * bus\_id (FK إلى `buses.id`)
     * timestamp (Timestamp)
     * status ENUM ('boarded','alighted')
     * location GEOGRAPHY(Point, 4326)
     * tenant\_id
   * **notifications**:

     * id (UUID)
     * type ENUM ('bus\_delay','attendance\_update','emergency\_alert','subscription\_reminder', ...)
     * title\_ar, title\_en, message\_ar, message\_en
     * recipient\_role (مثل: 'parent','school\_admin', ...)
     * recipient\_id (يمكن أن يكون Null إذا وُجّه للجميع)
     * is\_read (Boolean)
     * metadata (JSON)
     * created\_at, updated\_at
     * tenant\_id
   * **maintenance**:

     * id (UUID)
     * bus\_id (FK إلى `buses.id`)
     * description (Text)
     * scheduled\_date (Date)
     * status ENUM ('pending','in\_progress','completed')
     * tenant\_id
     * created\_at, updated\_at
   * **payments**:

     * id (UUID)
     * student\_id (FK إلى `students.id`)
     * amount (Decimal)
     * type ENUM ('subscription','penalty')
     * method ENUM ('paymob','fawry','stripe')
     * status ENUM ('success','failed')
     * transaction\_id (String)
     * paid\_at (Timestamp)
     * tenant\_id

2. **إنشاء الجداول وتفعيل RLS**:

   * في Supabase SQL Editor:

     ```sql
     -- مثال لإنشاء جدول students
     CREATE TABLE students (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       name_ar TEXT NOT NULL,
       name_en TEXT NOT NULL,
       parent_id UUID REFERENCES parents(id),
       photo_url TEXT,
       files JSON,
       tenant_id UUID REFERENCES tenants(id),
       created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
     );

     ALTER TABLE students ENABLE ROW LEVEL SECURITY;
     CREATE POLICY "tenant_students" ON students
       USING (tenant_id = (
         SELECT tenant_id FROM users WHERE users.id = auth.uid()
       ));
     ```
   * تفعيل RLS لجميع الجداول المماثلة باستخدام `tenant_id` وفحصه عبر JWT أو `auth.uid()`.
   * مثال لجدول `routes`:

     ```sql
     CREATE TABLE routes (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       name TEXT NOT NULL,
       bus_id UUID REFERENCES buses(id),
       driver_id UUID REFERENCES drivers(id),
       start_time TIME,
       end_time TIME,
       tenant_id UUID REFERENCES tenants(id),
       created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
     );

     ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
     CREATE POLICY "tenant_routes" ON routes
       USING (tenant_id = (
         SELECT tenant_id FROM users WHERE users.id = auth.uid()
       ));
     ```
   * تكرار لكل الجداول الأخرى (parents, drivers, buses, stops, attendance, notifications, maintenance, payments).

3. \*\*توليد وثيقة \*\*\`\`:

   * وصف تفصيلي لكل جدول، الحقول، العلاقات.
   * شرح نوع الحقول الجغرافية وكيفية استخدامها مع PostGIS.
   * مثال على استعلام يستخدم بيانات جغرافية (مثل: حساب المسافة بين نقطتين).

4. **توثيق وتسجيل التغييرات**:

   * **docs/details/database.md**:

     * شرح بنية الجداول والعلاقات.
     * أمثلة SQL للاستعلامات الأساسية.
     * توضيح سياسات RLS لكل جدول.
   * **docs/changelog/database.md**:

     * سجل إنشاء الجداول وتفعيل سياسات RLS.

5. **اختبارات قاعدة البيانات**:

   * سكربت للتحقق من عزل البيانات:

     * إدخال بيانات طالب ضمن Tenant A.
     * محاولة قراءة بيانات الطالب عبر مستخدم ضمن Tenant B => يتوقع 0 نتائج.
   * استخدام Postman لإرسال طلبات API ومحاولة تجاوز RLS (مثلاً: إرسال `tenant_id` يدويًا).

### الوثائق المطلوبة

* docs/changelog/database.md
* docs/details/database.md

---

## المرحلة 5: إعداد بنية واجهات المستخدم الأساسية (Frontend Scaffolding & Theming)

### الهدف الوظيفي

* إنشاء بنية مشروع React + TypeScript وضبط TailwindCSS، Radix UI، Lucide React.
* إعداد نظام الألوان (Design Tokens) والخطوط (Cairo)، ودعم Dark Mode وRTL.

### المهام التفصيلية

1. **تهيئة مشروع Vite + React + TypeScript**:

   ```bash
   npm create vite@latest school-system-frontend --template react-ts
   cd school-system-frontend
   npm install
   ```
2. **تركيب TailwindCSS**:

   ```bash
   npm install -D tailwindcss postcss autoprefixer
   npx tailwindcss init -p
   ```

   * تحديث `tailwind.config.js`:

     ```js
     module.exports = {
       content: ["./src/**/*.{js,ts,jsx,tsx}"],
       darkMode: 'class',
       theme: {
         extend: {
           colors: {
             primary: '#3b82f6',
             secondary: '#14b8a6',
             accent: '#10b981',
             warning: '#f59e0b',
             error: '#ef4444',
           },
           fontFamily: {
             sans: ['Cairo', 'sans-serif'],
           },
         },
       },
       plugins: [],
     }
     ```
   * إضافة رابط خط Cairo في `index.html`:

     ```html
     <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet" />
     ```
3. \*\*ضبط بنية \*\*\`\`:

   * إنشاء المجلد وملف Button.tsx كمكوّن أساسي:

     ```tsx
     // src/ui/components/Button.tsx
     import React, { ButtonHTMLAttributes, ReactNode } from 'react';

     interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
       children: ReactNode;
       variant?: 'primary' | 'secondary' | 'accent' | 'warning' | 'error';
     }

     const colorMap = {
       primary: 'bg-primary text-white hover:bg-opacity-90',
       secondary: 'bg-secondary text-white hover:bg-opacity-90',
       accent: 'bg-accent text-white hover:bg-opacity-90',
       warning: 'bg-warning text-white hover:bg-opacity-90',
       error: 'bg-error text-white hover:bg-opacity-90',
     };

     const Button: React.FC<ButtonProps> = ({ children, variant = 'primary', ...props }) => {
       return (
         <button
           className={`py-2 px-4 rounded-2xl shadow-md transition-all duration-200 ${colorMap[variant]}`}
           {...props}
         >
           {children}
         </button>
       );
     };

     export default Button;
     ```
   * إنشاء `Input.tsx`, `Modal.tsx` بنفس النمط (استخدام فئات Tailwind).
4. **تركيب Radix UI وLucide React**:

   ```bash
   npm install @radix-ui/react-dialog @radix-ui/react-select @radix-ui/react-tabs lucide-react
   ```

   * مثال لاستخدام Radix Dialog في `Modal.tsx`.
5. **إعداد i18n**:

   * تركيب المكتبات:

     ```bash
     npm install i18next react-i18next i18next-browser-languagedetector
     ```
   * ملف `src/i18n.ts`:

     ```ts
     import i18n from 'i18next';
     import { initReactI18next } from 'react-i18next';
     import LanguageDetector from 'i18next-browser-languagedetector';

     i18n
       .use(LanguageDetector)
       .use(initReactI18next)
       .init({
         fallbackLng: 'en',
         resources: {
           en: { common: require('../locales/en/common.json') },
           ar: { common: require('../locales/ar/common.json') },
         },
         ns: ['common'],
         defaultNS: 'common',
         interpolation: { escapeValue: false },
       });

     export default i18n;
     ```
   * إنشاء ملفات الترجمة:

     ```
     src/locales/en/common.json
     src/locales/ar/common.json
     ```
   * مثال:

     ```json
     // common.json (إنجليزي)
     {
       "login.title": "Login",
       "login.email": "Email",
       "login.password": "Password",
       "login.submit": "Login"
     }

     // common.json (عربي)
     {
       "login.title": "تسجيل الدخول",
       "login.email": "البريد الإلكتروني",
       "login.password": "كلمة المرور",
       "login.submit": "دخول"
     }
     ```
